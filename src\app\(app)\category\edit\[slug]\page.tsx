'use client';

import FileUpload from '@/components/image/image-upload';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useUpdateCategory } from '@/modules/category/mutations/update-category';
import { useGetCategory } from '@/modules/category/queries/list-category';
import { useRouter, useParams } from 'next/navigation';
import React, { useState, useEffect, FormEvent } from 'react';

const EditCategoryPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const slug = typeof params.slug === 'string' ? params.slug : '';

  const { data, isLoading, isError } = useGetCategory();
  const updateCategoryMutation = useUpdateCategory();

  // State for form inputs
  const [name, setName] = useState('');
  const [newSlug, setNewSlug] = useState('');
  const [image, setImage] = useState<string | null>(null);
  const [showOnNav, setShowOnNav] = useState(false);
  const [categoryId, setCategoryId] = useState<string | null>(null); // store id for update

  // When data loads or slug changes, find category and populate form
  useEffect(() => {
    if (data?.data && slug) {
      const category = data.data.find((cat) => cat.slug === slug);
      if (category) {
        setName(category.name);
        setNewSlug(category.slug);
        setImage(category.image || null);
        setCategoryId(category.id);
        setShowOnNav(category.showOnNav);
      }
    }
  }, [data, slug]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!categoryId) return;

    updateCategoryMutation.mutate(
      { id: categoryId, image: image, name, slug: newSlug, showOnNav },
      {
        onSuccess: () => router.push('/category'),
      }
    );
  };

  if (isLoading) return <div className="p-6">Loading category...</div>;
  if (isError || !categoryId)
    return <div className="p-6 text-red-600">Category not found</div>;

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen ">
      <h1 className="text-2xl font-semibold mb-6">Edit Category</h1>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label
            htmlFor="name"
            className="block mb-2 font-medium text-gray-700"
          >
            Name:
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter category name"
          />
        </div>
        <div>
          <label
            htmlFor="slug"
            className="block mb-2 font-medium text-gray-700"
          >
            Slug:
          </label>
          <input
            id="slug"
            type="text"
            value={newSlug}
            onChange={(e) => setNewSlug(e.target.value)}
            required
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter category slug"
          />
        </div>
        <div>
          <label
            htmlFor="image"
            className="block mb-2 font-medium text-gray-700"
          >
            Image:
          </label>
          <FileUpload
            accept="image/*"
            label="Upload Image"
            multiple={false}
            onChange={(url) => setImage(Array.isArray(url) ? url[0] : url)}
          />
        </div>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="showOnNav"
              checked={showOnNav}
              onCheckedChange={(checked) => setShowOnNav(!!checked)}
            />
            <Label
              htmlFor="showOnNav"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Show on Nav
            </Label>
          </div>
        </div>
        <Button
          type="submit"
          className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibold"
        >
          Save
        </Button>
      </form>
    </div>
  );
};

export default EditCategoryPage;
