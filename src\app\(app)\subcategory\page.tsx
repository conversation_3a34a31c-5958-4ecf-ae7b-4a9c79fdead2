'use client';

import { useState, useMemo } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Plus, Edit, Trash2, Search } from 'lucide-react';
import { useGetSubCategory } from '@/modules/subcategory/queries/get-all-subcategory';
import { useDeleteSubCategory } from '@/modules/subcategory/mutations/delete-subcategory';
import { useGetCategory } from '@/modules/category/queries/list-category';

export default function SubcategoryListPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const { data, isLoading, isError } = useGetSubCategory();
  const { data: categoryData } = useGetCategory();
  const deleteSubCategory = useDeleteSubCategory();

  const subcategories = data?.data ?? [];
  const categories = categoryData?.data ?? [];

  const filteredSubcategories = useMemo(() => {
    return subcategories.filter(
      (sc) =>
        sc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sc.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sc.categoryId.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [subcategories, searchTerm]);

  const getCategoryName = (id: string) => {
    const cat = categories.find((c) => c.id === id);
    return cat ? cat.name : 'Unknown';
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteSubCategory.mutateAsync(id);
    } catch (err) {
      console.error('Failed to delete subcategory:', err);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 text-center text-gray-500">
        Loading subcategories...
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto py-8 text-center text-red-600">
        Failed to load subcategories.
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subcategories</h1>
          <p className="text-muted-foreground">
            Manage your product subcategories
          </p>
        </div>
        <Button asChild>
          <Link href="/subcategory/create">
            <Plus className="mr-2 h-4 w-4" />
            Create Subcategory
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>All Subcategories</CardTitle>
              <CardDescription>{subcategories.length} total</CardDescription>
            </div>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search subcategories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredSubcategories.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm
                ? 'No subcategories match your search.'
                : 'No subcategories found.'}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Image</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubcategories.map((subcat) => (
                  <TableRow key={subcat.id}>
                    <TableCell className="font-medium">{subcat.name}</TableCell>
                    <TableCell>
                      <code className="text-sm bg-muted px-1 py-0.5 rounded">
                        {subcat.slug}
                      </code>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getCategoryName(subcat.categoryId)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {subcat.image && (
                        <img
                          src={subcat.image}
                          alt={subcat.name}
                          className="w-10 h-10 object-cover rounded"
                        />
                      ) || <span className="text-muted-foreground">No image</span>}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/subcategory/edit/${subcat.slug}`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will permanently delete {`${subcat.name}`}.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(subcat.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
