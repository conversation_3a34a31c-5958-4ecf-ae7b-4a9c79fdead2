import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useUpdateHome } from '../../mutations/updated-home';
import { useGetHomeBase } from '../../queries/get-home-base';

const EditDescription: React.FC = () => {
  const router = useRouter();
  const { data: homeData } = useGetHomeBase();
  const home = homeData?.data;
  const updateHome = useUpdateHome();

  const [description, setDescription] = useState('');

  useEffect(() => {
    if (home) {
      setDescription(home.description);
    }
  }, [home]);

  const handleUpdate = () => {
    if (!description.trim()) {
      alert('Please enter a description');
      return;
    }

    updateHome.mutate(
      { ...home,id: home?.id, description },
      {
        onSuccess() {
          alert('Description updated successfully');
          router.push('/home');
        },
        onError() {
          alert('Failed to update description');
        },
      }
    );
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Edit Description Section</h2>

      <div className="mb-6">
        <label className="block font-medium mb-2">Description</label>
        <Textarea
          className="w-full border rounded px-3 py-2"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter description"
          rows={5}
        />
      </div>

      <div className="mt-6 flex space-x-2">
        <Button onClick={handleUpdate} className="px-4 py-2 rounded text-white bg-green-600 hover:bg-green-700">
          Update
        </Button>
        <Button variant="secondary" onClick={() => router.push('/home')}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default EditDescription;
