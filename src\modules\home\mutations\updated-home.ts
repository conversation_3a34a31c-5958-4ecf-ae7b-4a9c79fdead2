import { IHome } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateHome() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHome>, Error, IHome>({
    mutationFn: (data: IHome) =>
      fetch(`/api/home`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['home'] });
      toast.success('Home Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Home');
    },
  });
}
