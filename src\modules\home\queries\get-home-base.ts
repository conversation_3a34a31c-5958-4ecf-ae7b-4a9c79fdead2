import { IHome } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetHomeBase() {
  return useQuery<IApiResponse<IHome>, Error>({
    queryKey: ['home'],
    queryFn: () =>
      fetch(`/api/home`, {
        mode: 'cors',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
      }).then((res) => res.json()),
  });
}
