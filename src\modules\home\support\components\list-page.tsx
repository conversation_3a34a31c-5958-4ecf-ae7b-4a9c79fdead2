'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Pen, Trash } from 'lucide-react';
import { UseGetSupport } from '../queries/use-get-home-support';
import { UseDeleteSupport } from '../mutations/use-delete-home-support';

const SupportSection: React.FC = () => {
    const { data, isLoading, isError } = UseGetSupport();

    const support = data?.data || null;

    const { mutate: deleteSupport } = UseDeleteSupport();

    const handleDelete = () => {
        if (window.confirm('Are you sure you want to delete this support item?')) {
            deleteSupport(undefined, {
                onSuccess: () => alert('Support item deleted successfully'),
                onError: () => alert('Failed to delete support item'),
            });
        }
    };
    if (isLoading) return <div>Loading support section…</div>;
    if (isError) return <div>Error loading support section.</div>;

    if (!support) {
        return (
            <div className="p-6">
                <p className="mb-6">No support section found.</p>
                <Link href="/home/<USER>/create">
                    <button className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition">
                        Create Support Section
                    </button>
                </Link>
            </div>
        );
    }

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-3xl font-bold">Support Section</h1>
                    <Link href="/home/<USER>/edit">
                        <Button>Edit Support Section</Button>
                    </Link>
                </div>
                <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-300 rounded">
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="border px-4 py-2">S.N.</th>
                                <th className="border px-4 py-2">Image</th>
                                <th className="border px-4 py-2">Title</th>
                                <th className="border px-4 py-2">Subtitle</th>
                                <th className="border px-4 py-2">Button Label</th>
                                <th className="border px-4 py-2">Button URL</th>
                                <th className="border px-4 py-2">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className="bg-white">
                                <td className="border px-4 py-2 text-center">1</td>
                                <td className="border px-4 py-2 text-center">
                                    {support.image ? (
                                        <Image
                                            src={support.image}
                                            alt={support.title}
                                            width={80}
                                            height={60}
                                            className="rounded object-cover inline-block"
                                        />
                                    ) : (
                                        'No Image'
                                    )}
                                </td>
                                <td className="border px-4 py-2">{support.title}</td>
                                <td className="border px-4 py-2">{support.subTitle}</td>
                                <td className="border px-4 py-2">{support.buttonLabel}</td>
                                <td className="border px-4 py-2">
                                    <Link
                                        href={support.buttonUrl}
                                        target="_blank"
                                        rel="noreferrer"
                                        className="text-blue-600 hover:underline"
                                    >
                                        {support.buttonUrl}
                                    </Link>
                                </td>
                                <td className="border px-4 py-2 flex gap-2 justify-center">
                                    <Link href={`/home/<USER>/edit`}>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                        >
                                            <Pen className="w-4 h-4" />
                                        </Button>
                                    </Link>
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={handleDelete}
                                        className="inline-flex items-center px-3 py-1 border bg-white border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                    >
                                        <Trash className="w-4 h-4" />
                                    </Button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </section>
    );
};

export default SupportSection;
