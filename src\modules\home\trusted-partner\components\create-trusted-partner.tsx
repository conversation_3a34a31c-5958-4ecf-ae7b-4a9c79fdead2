'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import FileUpload from '@/components/image/image-upload';
import { useCreateTrustedPartner } from '../mutations/create-trusted-partner';
import { Textarea } from '@/components/ui/textarea';

const TrustedPartnerCreatePage: React.FC = () => {
  const router = useRouter();
  const createTrustedPartner = useCreateTrustedPartner();

  const [description, setDescription] = useState('');
  const [images, setImages] = useState<string[]>([]);

  const handleSubmit = async () => {
    if (!description.trim()) {
      alert('Description is required');
      return;
    }
    if (images.length === 0) {
      alert('Please upload at least one image');
      return;
    }

    try {
      await createTrustedPartner.mutateAsync({
        id: '',
        description,
        images,
      });
      alert('Trusted Partner created successfully');
      router.push('/home');
    } catch (error) {
      alert('Failed to create trusted partner');
      console.error(error);
    }
  };


  return (
    <div className="min-h-screen bg-gray-50 p-6 container mx-auto">
      <h2 className="text-2xl font-semibold mb-6">Create Trusted Partner</h2>

      <div className="mb-6">
        <label className="block font-medium mb-2">Description</label>
        <Textarea
          className="w-full border rounded p-3 min-h-[120px]"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter description"
        />
      </div>

      <div className="mb-6">
        <FileUpload
          value={images}
          onChange={(urls) => {
            if (!urls) setImages([]);
            else if (Array.isArray(urls)) setImages(urls);
            else setImages([urls]);
          }}
          multiple={true}
          accept="image/*"
          label="Upload Image(s)"
        />
      </div>


      <div className="flex gap-4">
        <Button onClick={handleSubmit} className="bg-green-600 hover:bg-green-700 text-white">
          Create
        </Button>
        <Button onClick={() => router.push('/home')} variant="secondary">
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TrustedPartnerCreatePage;
