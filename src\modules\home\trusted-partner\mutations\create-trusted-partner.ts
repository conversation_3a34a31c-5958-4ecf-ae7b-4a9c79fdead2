import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { toast } from 'sonner';
import { ITrustedPartner } from '@/types/home';

export function useCreateTrustedPartner() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ITrustedPartner>, Error, ITrustedPartner>({
    mutationFn: (data: ITrustedPartner) =>
      fetch(`/api/home-trusted-partners`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trusted-partner'] });
      toast.success('Trusted-partner Created Sucessfully');
    },
    onError: () => {
      toast.error('Error Creating Trusted-partner');
    },
  });
}
