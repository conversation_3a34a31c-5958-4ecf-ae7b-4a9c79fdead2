import { ITrustedPartner } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export function UseDeleteTrustedPartner() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ITrustedPartner>, Error, void>({
    mutationFn: () =>
      fetch(`/api/home-trusted-partners`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trusted-partner'] });
    },
  });
}
