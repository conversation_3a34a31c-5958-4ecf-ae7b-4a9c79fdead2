import { IHomeSupport, ITrustedPartner } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateTrustedPartner() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<ITrustedPartner>, Error, ITrustedPartner>({
    mutationFn: (data: ITrustedPartner) =>
      fetch(`/api/home-trusted-partners`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trusted-partners'] });
      toast.success('Trusted-partners Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Trusted-partners');
    },
  });
}
