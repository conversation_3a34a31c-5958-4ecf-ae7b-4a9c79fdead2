import { ITrustedPartner } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetTrustedPartners() {
    return useQuery<IApiResponse<ITrustedPartner>, Error>({
        queryKey: ['trusted-partner'],
        queryFn: () =>
            fetch(`/api/home-trusted-partners`, {
                mode: 'cors',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
            }).then((res) => res.json()),
    });
}
