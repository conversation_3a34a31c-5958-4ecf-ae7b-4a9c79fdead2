export interface IHome {
  id: string;
  description: string;
  createdAt?: string;
  updatedAt?: string;
  homeHero?: IHero;
  homeHotList?: IHotList;
  homeService?: IHomeService[];
  homeSupport?: IHomeSupport;
  homeCapacity?: IHomeCapacity[];
  homeTrustedPartner?: ITrustedPartner;
}

export interface IHero {
  id: string;
  homeId?: string;
  titles: string[];
  images: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface IHotList {
  id: string;
  title: string;
  images: string[];
  points: string[];
  linkUrl: string;
  linkLabel: string;
}

export interface IHomeService {
  id: string;
  title: string;
  subtitle: string;
}

export interface IHomeSupport {
  id: string;
  title: string;
  subTitle: string;
  image?: string;
  buttonLabel: string;
  buttonUrl: string;
  homeId?: string;
}

export interface IHomeCapacity {
  id: string;
  title: string;
  icon: string;
}

export interface ITrustedPartner {
  id: string;
  description: string;
  images: string[];
}
